import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../../../core/shared/widgets/currency_input_field.dart';
import '../../../../core/shared/widgets/settings_toggle.dart';
import '../controllers/settings_controller.dart';

/// Notifications settings screen
class NotificationsSettingsScreen extends StatefulWidget {
  /// Constructor
  const NotificationsSettingsScreen({super.key});

  @override
  State<NotificationsSettingsScreen> createState() =>
      _NotificationsSettingsScreenState();
}

class _NotificationsSettingsScreenState
    extends State<NotificationsSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Alerts & Notifications'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: Consumer<SettingsController>(
        builder: (context, controller, _) {
          return ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              // Alert Threshold section
              Card(
                margin: const EdgeInsets.only(bottom: 16.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.warning, color: Colors.amber),
                          const SizedBox(width: 16),
                          const Text(
                            'Alert Threshold',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            'Current: ${controller.currencySymbol}${controller.alertThreshold.toStringAsFixed(2)}',
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  Theme.of(context).textTheme.bodySmall?.color,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Get notified when balance falls below this amount',
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Alert threshold input
                      CurrencyInputField(
                        value: controller.alertThreshold,
                        onChanged: (value) {
                          if (value != null &&
                              value >= 1.00 &&
                              value <= 999.99) {
                            Provider.of<SettingsController>(context,
                                    listen: false)
                                .updateAlertThreshold(value);
                          }
                        },
                        currencySymbol: controller.currencySymbol,
                        labelText: 'Alert Threshold',
                        helperText:
                            'Enter a value between ${controller.currencySymbol} 1.00 and ${controller.currencySymbol} 999.99',
                        minValue: 1.00,
                        maxValue: 999.99,
                      ),
                    ],
                  ),
                ),
              ),

              // Days in Advance section
              Card(
                margin: const EdgeInsets.only(bottom: 16.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.timelapse, color: Colors.blue),
                          const SizedBox(width: 16),
                          const Text(
                            'Days in Advance',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            'Current: ${controller.daysInAdvance} days',
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  Theme.of(context).textTheme.bodySmall?.color,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Get notified this many days before you run out',
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Days in advance input
                      Builder(
                        builder: (context) {
                          final textController = TextEditingController(
                              text: controller.daysInAdvance.toString());

                          return TextField(
                            controller: textController,
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            decoration: const InputDecoration(
                              labelText: 'Days in Advance',
                              border: OutlineInputBorder(),
                              helperText: 'Enter a value between 1 and 99 days',
                              suffixText: 'days',
                            ),
                            onChanged: (value) {
                              if (value.isNotEmpty) {
                                final newValue = int.tryParse(value);
                                if (newValue != null &&
                                    newValue >= 1 &&
                                    newValue <= 99) {
                                  Provider.of<SettingsController>(context,
                                          listen: false)
                                      .updateDaysInAdvance(newValue);
                                }
                              }
                            },
                            showCursor: true,
                            autofocus: false,
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),

              // Notifications section
              Card(
                margin: const EdgeInsets.only(bottom: 16.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.notifications, color: Colors.blue),
                          const SizedBox(width: 16),
                          const Text(
                            'Notifications',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Configure notification preferences',
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Notifications toggle
                      SettingsToggle(
                        title: 'Enable Notifications',
                        description: 'Master toggle for all notifications',
                        value: controller.notificationsEnabled,
                        onChanged: (value) {
                          Provider.of<SettingsController>(context,
                                  listen: false)
                              .updateNotificationsEnabled(value);
                        },
                      ),

                      if (controller.notificationsEnabled) ...[
                        const Divider(),
                        SettingsToggle(
                          title: 'Low Balance Alerts',
                          description:
                              'Alerts when you have less than 24 hours of credit remaining',
                          value: true,
                          onChanged: (value) {
                            Provider.of<SettingsController>(context,
                                    listen: false)
                                .updateLowBalanceAlertsEnabled(value);
                          },
                        ),
                        SettingsToggle(
                          title: 'Time to Top Up Alerts',
                          description:
                              'Alerts when your alert threshold will be reached in your specified days in advance',
                          value: true,
                          onChanged: (value) {
                            Provider.of<SettingsController>(context,
                                    listen: false)
                                .updateTimeToTopUpAlertsEnabled(value);
                          },
                        ),
                        SettingsToggle(
                          title: 'Invalid Record Alerts',
                          description:
                              'Alerts for suspicious or invalid entries',
                          value: true,
                          onChanged: (value) {
                            Provider.of<SettingsController>(context,
                                    listen: false)
                                .updateInvalidRecordAlertsEnabled(value);
                          },
                        ),
                      ],
                    ],
                  ),
                ),
              ),

              // Reminders section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.alarm, color: Colors.blue),
                          SizedBox(width: 16),
                          Text(
                            'Reminders',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Set up reminders to check your meter',
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Reminders toggle
                      SettingsToggle(
                        title: 'Enable Meter Reminders',
                        description: 'Master toggle for reminders',
                        value: controller.remindersEnabled,
                        onChanged: (value) {
                          Provider.of<SettingsController>(context,
                                  listen: false)
                              .updateRemindersEnabled(value);
                        },
                      ),

                      if (controller.remindersEnabled) ...[
                        const Divider(),
                        // Reminder frequency
                        const ListTile(
                          title: Text('Reminder Frequency'),
                          subtitle: Text('How often to remind you'),
                        ),
                        RadioListTile<String>(
                          title: const Text('Daily'),
                          value: 'daily',
                          groupValue: 'daily',
                          onChanged: (value) {
                            if (value != null) {
                              Provider.of<SettingsController>(context,
                                      listen: false)
                                  .updateReminderFrequency(value);
                            }
                          },
                        ),
                        RadioListTile<String>(
                          title: const Text('Weekly'),
                          value: 'weekly',
                          groupValue: 'daily',
                          onChanged: (value) {
                            if (value != null) {
                              Provider.of<SettingsController>(context,
                                      listen: false)
                                  .updateReminderFrequency(value);
                            }
                          },
                        ),
                        RadioListTile<String>(
                          title: const Text('Bi-weekly'),
                          value: 'bi-weekly',
                          groupValue: 'daily',
                          onChanged: (value) {
                            if (value != null) {
                              Provider.of<SettingsController>(context,
                                      listen: false)
                                  .updateReminderFrequency(value);
                            }
                          },
                        ),
                        RadioListTile<String>(
                          title: const Text('Monthly'),
                          value: 'monthly',
                          groupValue: 'daily',
                          onChanged: (value) {
                            if (value != null) {
                              Provider.of<SettingsController>(context,
                                      listen: false)
                                  .updateReminderFrequency(value);
                            }
                          },
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
