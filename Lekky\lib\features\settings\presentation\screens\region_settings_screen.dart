import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/settings_controller.dart';
import '../../../../core/shared/widgets/settings_radio.dart';

/// Region settings screen
class RegionSettingsScreen extends StatefulWidget {
  /// Constructor
  const RegionSettingsScreen({super.key});

  @override
  State<RegionSettingsScreen> createState() => _RegionSettingsScreenState();
}

class _RegionSettingsScreenState extends State<RegionSettingsScreen> {
  late String _selectedLanguage;
  late String _selectedCurrency;
  late String _selectedCurrencySymbol;

  @override
  void initState() {
    super.initState();
    final controller = Provider.of<SettingsController>(context, listen: false);
    _selectedLanguage = controller.language;
    _selectedCurrency = controller.currency;
    _selectedCurrencySymbol = controller.currencySymbol;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Region'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: Consumer<SettingsController>(
        builder: (context, controller, _) {
          return ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              // Language section
              Card(
                margin: const EdgeInsets.only(bottom: 16.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.language, color: Colors.blue),
                          const SizedBox(width: 16),
                          const Text(
                            'Language',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            'Current: $_selectedLanguage',
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  Theme.of(context).textTheme.bodySmall?.color,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Select your preferred language',
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Language options
                      _buildLanguageOption('English'),
                      _buildLanguageOption('Spanish'),
                      _buildLanguageOption('French'),
                      _buildLanguageOption('German'),
                      _buildLanguageOption('Italian'),
                      _buildLanguageOption('Portuguese'),
                      _buildLanguageOption('Russian'),
                      _buildLanguageOption('Chinese'),
                      _buildLanguageOption('Japanese'),
                    ],
                  ),
                ),
              ),

              // Currency section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.attach_money, color: Colors.blue),
                          const SizedBox(width: 16),
                          const Text(
                            'Currency',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            'Current: $_selectedCurrencySymbol',
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  Theme.of(context).textTheme.bodySmall?.color,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Select your preferred currency',
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Currency options in two columns
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // First column
                          Expanded(
                            child: Column(
                              children: [
                                _buildCurrencyOption('GBP', '£'),
                                _buildCurrencyOption('USD', '\$'),
                                _buildCurrencyOption('EUR', '€'),
                                _buildCurrencyOption('CNY', 'CN¥'),
                                _buildCurrencyOption('INR', '₹'),
                              ],
                            ),
                          ),
                          // Second column
                          Expanded(
                            child: Column(
                              children: [
                                _buildCurrencyOption('JPY', '¥'),
                                _buildCurrencyOption('BRL', 'R\$'),
                                _buildCurrencyOption('RUB', '₽'),
                                _buildCurrencyOption('CAD', 'C\$'),
                                _buildCurrencyOption('AUD', 'A\$'),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildLanguageOption(String language) {
    return RadioListTile<String>(
      title: Text(language),
      value: language,
      groupValue: _selectedLanguage,
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedLanguage = value;
          });
          Provider.of<SettingsController>(context, listen: false)
              .updateLanguage(value);
        }
      },
    );
  }

  Widget _buildCurrencyOption(String currency, String symbol) {
    return RadioListTile<String>(
      title: Text('$symbol$currency'),
      value: currency,
      groupValue: _selectedCurrency,
      dense: true,
      contentPadding: const EdgeInsets.symmetric(horizontal: 8),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedCurrency = value;
            _selectedCurrencySymbol = symbol;
          });
          Provider.of<SettingsController>(context, listen: false)
              .updateCurrency(value, symbol);
        }
      },
    );
  }
}
