import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../utils/date_formatter.dart';
import 'settings_provider.dart';

part 'date_formatter_provider.g.dart';

/// Provider that watches settings and provides date formatting methods
/// This ensures date formatting updates when user changes preferences
@riverpod
class DateFormatterNotifier extends _$DateFormatterNotifier {
  @override
  DateFormatterService build() {
    // Watch settings to rebuild when date format changes
    final settings = ref.watch(settingsProvider);
    
    return DateFormatterService(settings);
  }
}

/// Service class that provides date formatting methods
/// Uses current settings state for formatting
class DateFormatterService {
  final AsyncValue<dynamic> _settingsAsync;
  
  DateFormatterService(this._settingsAsync);
  
  /// Format a date according to user preferences
  String formatDate(DateTime date, {bool forceShowTime = false}) {
    return DateFormatter.formatDate(date, forceShowTime: forceShowTime);
  }
  
  /// Format a date with time for display in add/edit entry dialogs
  String formatDateTimeForEntry(DateTime date) {
    return DateFormatter.formatDateTimeForEntry(date);
  }
  
  /// Format a date for display in the meter reading info card
  String formatDateForMeterInfo(DateTime date) {
    return DateFormatter.formatDateForMeterInfo(date);
  }
  
  /// Format a date for chart data points and axis labels
  String formatDateForChart(DateTime date) {
    return DateFormatter.formatDateForChart(date);
  }
  
  /// Format a date for chart data points with year when needed
  String formatDateForChartWithYear(DateTime date) {
    return DateFormatter.formatDateForChartWithYear(date);
  }
  
  /// Format a date for validation error messages
  String formatDateForValidation(DateTime date) {
    return DateFormatter.formatDateForValidation(date);
  }
  
  /// Format a date for dashboard components (compact format)
  String formatDateForDashboard(DateTime date) {
    return DateFormatter.formatDateForDashboard(date);
  }
  
  /// Format a date for history table entries
  String formatDateForHistory(DateTime date) {
    return DateFormatter.formatDateForHistory(date);
  }
  
  /// Format a short date for compact displays
  String formatDateShort(DateTime date) {
    return DateFormatter.formatDateShort(date);
  }
}

/// Convenience provider for quick access to date formatting
@riverpod
DateFormatterService dateFormatter(DateFormatterRef ref) {
  return ref.watch(dateFormatterNotifierProvider);
}
