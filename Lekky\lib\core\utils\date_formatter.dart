import 'package:flutter/material.dart';
import 'package:intl/intl.dart' as intl;
import '../di/service_locator.dart';
import '../services/preference_service.dart';
import '../shared/models/date_format.dart' as app_format;

/// Utility class for formatting dates according to user preferences
class DateFormatter {
  /// Get the PreferenceService instance
  static PreferenceService? _getPreferenceService() {
    try {
      if (serviceLocator.isRegistered<PreferenceService>()) {
        return serviceLocator<PreferenceService>();
      }
    } catch (e) {
      debugPrint('Error getting PreferenceService: $e');
    }
    return null;
  }

  /// Format a date according to user preferences
  ///
  /// If [forceShowTime] is true, time will be shown regardless of user preferences
  /// This is used for the add/edit entry dialogs
  static String formatDate(DateTime date, {bool forceShowTime = false}) {
    final preferenceService = _getPreferenceService();

    if (preferenceService == null) {
      // Fallback to default format if preference service is not available
      return intl.DateFormat('dd-MM-yyyy').format(date);
    }

    final dateFormat = preferenceService.dateFormat;
    final showTimeWithDate =
        forceShowTime || preferenceService.showTimeWithDate;

    String formattedDate;

    // Format the date according to the user's preference
    switch (dateFormat) {
      case app_format.DateFormat.ddMmYyyy:
        formattedDate = intl.DateFormat('dd-MM-yyyy').format(date);
        break;
      case app_format.DateFormat.mmDdYyyy:
        formattedDate = intl.DateFormat('MM-dd-yyyy').format(date);
        break;
      case app_format.DateFormat.yyyyMmDd:
        formattedDate = intl.DateFormat('yyyy-MM-dd').format(date);
        break;
    }

    // Add time if needed
    if (showTimeWithDate) {
      final formattedTime = intl.DateFormat('HH:mm').format(date);
      formattedDate = '$formattedDate $formattedTime';
    }

    return formattedDate;
  }

  /// Format a date with time for display in add/edit entry dialogs
  static String formatDateTimeForEntry(DateTime date) {
    return formatDate(date, forceShowTime: true);
  }

  /// Format a date for display in the meter reading info card
  static String formatDateForMeterInfo(DateTime date) {
    final preferenceService = _getPreferenceService();

    if (preferenceService == null) {
      // Fallback to default format if preference service is not available
      return intl.DateFormat('dd MMM yyyy, HH:mm').format(date);
    }

    // For meter info, we always show the month name and time
    final dateFormat = preferenceService.dateFormat;

    switch (dateFormat) {
      case app_format.DateFormat.ddMmYyyy:
        return intl.DateFormat('dd MMM yyyy, HH:mm').format(date);
      case app_format.DateFormat.mmDdYyyy:
        return intl.DateFormat('MMM dd yyyy, HH:mm').format(date);
      case app_format.DateFormat.yyyyMmDd:
        return intl.DateFormat('yyyy MMM dd, HH:mm').format(date);
    }
  }
}
